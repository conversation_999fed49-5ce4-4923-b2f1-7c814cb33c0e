{"borrowedAmountOutsideElevationGroup": {"data": "374157146081220", "type": "u64"}, "borrowedAmountsAgainstThisReserveInElevationGroups": {"data": ["0", "0", "18510795996", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "type": {"array": ["u64", 32]}}, "collateral": {"data": {"mintPubkey": {"data": "B8V6WVjPxW1UGwVDfxH2d2r8SyT4cqn7dQRK6XneVa7D", "type": "public<PERSON>ey"}, "mintTotalSupply": {"data": "455968423545172", "type": "u64"}, "padding1": {"data": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "type": {"array": ["u128", 32]}}, "padding2": {"data": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "type": {"array": ["u128", 32]}}, "supplyVault": {"data": "3DzjXRfxRm6iejfyyMynR4tScddaanrePJ1NJU2XnPPL", "type": "public<PERSON>ey"}}, "type": {"defined": "ReserveCollateral"}}, "config": {"data": {"assetTier": {"data": 0, "type": "u8"}, "autodeleverageEnabled": {"data": 0, "type": "u8"}, "badDebtLiquidationBonusBps": {"data": "99", "type": "u16"}, "borrowFactorPct": {"data": "100", "type": "u64"}, "borrowLimit": {"data": "560000000000000", "type": "u64"}, "borrowLimitAgainstThisCollateralInElevationGroup": {"data": ["0", "0", "18446744073709551615", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "type": {"array": ["u64", 32]}}, "borrowLimitOutsideElevationGroup": {"data": "18446744073709551615", "type": "u64"}, "borrowRateCurve": {"data": {"points": {"data": [{"borrowRateBps": 0, "utilizationRateBps": 0}, {"borrowRateBps": 475, "utilizationRateBps": 9500}, {"borrowRateBps": 3000, "utilizationRateBps": 10000}, {"borrowRateBps": 3000, "utilizationRateBps": 10000}, {"borrowRateBps": 3000, "utilizationRateBps": 10000}, {"borrowRateBps": 3000, "utilizationRateBps": 10000}, {"borrowRateBps": 3000, "utilizationRateBps": 10000}, {"borrowRateBps": 3000, "utilizationRateBps": 10000}, {"borrowRateBps": 3000, "utilizationRateBps": 10000}, {"borrowRateBps": 3000, "utilizationRateBps": 10000}, {"borrowRateBps": 3000, "utilizationRateBps": 10000}], "type": {"array": [{"defined": "CurvePoint"}, 11]}}}, "type": {"defined": "BorrowRateCurve"}}, "debtWithdrawalCap": {"data": {"configCapacity": {"data": "200000000000000", "type": "i64"}, "configIntervalLengthSeconds": {"data": "86400", "type": "u64"}, "currentTotal": {"data": "-109373170836", "type": "i64"}, "lastIntervalStartTimestamp": {"data": "1756566618", "type": "u64"}}, "type": {"defined": "WithdrawalCaps"}}, "deleveragingBonusIncreaseBpsPerDay": {"data": "0", "type": "u64"}, "deleveragingMarginCallPeriodSecs": {"data": "604800", "type": "u64"}, "deleveragingThresholdDecreaseBpsPerDay": {"data": "7200", "type": "u64"}, "depositLimit": {"data": "640000000000000", "type": "u64"}, "depositWithdrawalCap": {"data": {"configCapacity": {"data": "200000000000000", "type": "i64"}, "configIntervalLengthSeconds": {"data": "86400", "type": "u64"}, "currentTotal": {"data": "3609625930082", "type": "i64"}, "lastIntervalStartTimestamp": {"data": "1756552234", "type": "u64"}}, "type": {"defined": "WithdrawalCaps"}}, "disableUsageAsCollOutsideEmode": {"data": 0, "type": "u8"}, "elevationGroups": {"data": [1, 3, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "type": {"array": ["u8", 20]}}, "fees": {"data": {"borrowFeeSf": {"data": "0", "type": "u64"}, "flashLoanFeeSf": {"data": "11529215046068", "type": "u64"}, "padding": {"data": [0, 0, 0, 0, 0, 0, 0, 0], "type": {"array": ["u8", 8]}}}, "type": {"defined": "ReserveFees"}}, "hostFixedInterestRateBps": {"data": "0", "type": "u16"}, "liquidationThresholdPct": {"data": 90, "type": "u8"}, "loanToValuePct": {"data": 80, "type": "u8"}, "maxLiquidationBonusBps": {"data": "1000", "type": "u16"}, "minLiquidationBonusBps": {"data": "500", "type": "u16"}, "protocolLiquidationFeePct": {"data": 50, "type": "u8"}, "protocolOrderExecutionFeePct": {"data": 0, "type": "u8"}, "protocolTakeRatePct": {"data": 10, "type": "u8"}, "reserved1": {"data": [0], "type": {"array": ["u8", 1]}}, "reserved2": {"data": [0, 0, 0, 0, 0, 0, 0, 0, 0], "type": {"array": ["u8", 9]}}, "status": {"data": 0, "type": "u8"}, "tokenInfo": {"data": {"blockPriceUsage": {"data": 0, "type": "u8"}, "heuristic": {"data": {"exp": {"data": "2", "type": "u64"}, "lower": {"data": "90", "type": "u64"}, "upper": {"data": "105", "type": "u64"}}, "type": {"defined": "PriceHeuristic"}}, "maxAgePriceSeconds": {"data": "180", "type": "u64"}, "maxAgeTwapSeconds": {"data": "240", "type": "u64"}, "maxTwapDivergenceBps": {"data": "300", "type": "u64"}, "name": {"data": [85, 83, 68, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "type": {"array": ["u8", 32]}}, "padding": {"data": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "type": {"array": ["u64", 19]}}, "pythConfiguration": {"data": {"price": {"data": "11111111111111111111111111111111", "type": "public<PERSON>ey"}}, "type": {"defined": "PythConfiguration"}}, "reserved": {"data": [0, 0, 0, 0, 0, 0, 0], "type": {"array": ["u8", 7]}}, "scopeConfiguration": {"data": {"priceChain": {"data": [20, 65535, 65535, 65535], "type": {"array": ["u16", 4]}}, "priceFeed": {"data": "3NJYftD5sjVfxSnUdZ1wVML8f3aC6mp1CXCL6L7TnU8C", "type": "public<PERSON>ey"}, "twapChain": {"data": [62, 65535, 65535, 65535], "type": {"array": ["u16", 4]}}}, "type": {"defined": "ScopeConfiguration"}}, "switchboardConfiguration": {"data": {"priceAggregator": {"data": "11111111111111111111111111111111", "type": "public<PERSON>ey"}, "twapAggregator": {"data": "11111111111111111111111111111111", "type": "public<PERSON>ey"}}, "type": {"defined": "SwitchboardConfiguration"}}}, "type": {"defined": "TokenInfo"}}, "utilizationLimitBlockBorrowingAbovePct": {"data": 0, "type": "u8"}}, "type": {"defined": "ReserveConfig"}}, "configPadding": {"data": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "type": {"array": ["u64", 116]}}, "farmCollateral": {"data": "JAvnB9AKtgPsTEoKmn24Bq64UMoYcrtWtq42HHBdsPkh", "type": "public<PERSON>ey"}, "farmDebt": {"data": "11111111111111111111111111111111", "type": "public<PERSON>ey"}, "lastUpdate": {"data": {"placeholder": {"data": [0, 0, 0, 0, 0, 0], "type": {"array": ["u8", 6]}}, "priceStatus": {"data": 63, "type": "u8"}, "slot": {"data": "155", "type": "u64"}, "stale": {"data": 0, "type": "u8"}}, "type": {"defined": "LastUpdate"}}, "lendingMarket": {"data": "7u3HeHxYDLhnCoErrtycNokbQYbWGzLs6JSDqGAv5PfF", "type": "public<PERSON>ey"}, "liquidity": {"data": {"absoluteReferralRateSf": {"data": "0", "type": "u128"}, "accumulatedProtocolFeesSf": {"data": "1199197258857674967611388911068", "type": "u128"}, "accumulatedReferrerFeesSf": {"data": "0", "type": "u128"}, "availableAmount": {"data": "150899649385243", "type": "u64"}, "borrowLimitCrossedTimestamp": {"data": "0", "type": "u64"}, "borrowedAmountSf": {"data": "4332483024651737796640540556802311", "type": "u128"}, "cumulativeBorrowRateBsf": {"data": {"padding": {"data": ["0", "0"], "type": {"array": ["u64", 2]}}, "value": {"data": ["1412007458627000448", "0", "0", "0"], "type": {"array": ["u64", 4]}}}, "type": {"defined": "BigFractionBytes"}}, "depositLimitCrossedTimestamp": {"data": "0", "type": "u64"}, "feeVault": {"data": "BbDUrk1bVtSixgQsPLBJFZEF7mwGstnD5joA1WzYvYFX", "type": "public<PERSON>ey"}, "marketPriceLastUpdatedTs": {"data": "1756616305", "type": "u64"}, "marketPriceSf": {"data": "1152771555635957809", "type": "u128"}, "mintDecimals": {"data": "6", "type": "u64"}, "mintPubkey": {"data": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "type": "public<PERSON>ey"}, "padding2": {"data": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "type": {"array": ["u64", 51]}}, "padding3": {"data": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "type": {"array": ["u128", 32]}}, "pendingReferrerFeesSf": {"data": "0", "type": "u128"}, "supplyVault": {"data": "Bgq7trRgVMeq33yt235zM2onQ4bRDBsY5EWiTetF4qw6", "type": "public<PERSON>ey"}, "tokenProgram": {"data": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "type": "public<PERSON>ey"}}, "type": {"defined": "ReserveLiquidity"}}, "padding": {"data": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "type": {"array": ["u64", 207]}}, "reserveCollateralPadding": {"data": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "type": {"array": ["u64", 150]}}, "reserveLiquidityPadding": {"data": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "type": {"array": ["u64", 150]}}, "version": {"data": "1", "type": "u64"}}